# 基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化 - 研究方法论

## 1. 研究目标与核心创新

### 1.1 研究目标
设计并实现一个基于单层AGV-Task-AGV注意力机制增强的MAPPO算法，用于解决多载重AGV协同调度问题，实现AGV自主任务选择和载重优化。

**核心实现目标**：
1. **自主任务选择**：多个AGV能够基于自身观测自主选择最适合的任务
2. **载重优化**：在不超过最大载重限制的前提下，最大化载重利用率
3. **路径平衡**：在载重优化的同时平衡路径长度，避免过度绕行
4. **协作调度**：多AGV间的智能协作，避免冲突和资源竞争

**技术实现目标**：
1. **单层统一注意力机制**：通过AGV-Task-AGV异构图注意力同时实现任务分配和协作感知
2. **多载重决策**：支持AGV同时携带多个任务的决策机制
3. **动态负载均衡**：实时优化系统整体的载重分配和路径效率

### 1.2 核心创新点
- **单层AGV-Task-AGV注意力机制**：统一处理任务分配和AGV协作感知，简化架构
- **MAPPO深度融合**：将统一注意力机制集成到策略网络和价值网络中
- **渐进式课程学习**：通过6阶段学习实现从简单到复杂的技能掌握
- **约束增强异构图注意力**：融合物理约束和业务约束的统一注意力计算

### 1.3 研究假设
1. **自主选择假设**：基于统一注意力机制的任务分配能够让AGV自主选择最优任务组合
2. **载重优化假设**：单层AGV-Task-AGV注意力机制能够在载重约束下实现最优的载重利用率
3. **路径平衡假设**：统一的协作感知注意力能够平衡载重优化与路径长度的权衡
4. **多载重协作假设**：多个携带不同载重的AGV能够通过异构图注意力实现有效的协作调度

## 2. 技术架构设计

### 2.1 整体框架
采用Ray RLlib作为基础框架，实现"中心化训练，分布式执行"的MAPPO算法，集成单层AGV-Task-AGV注意力机制。

**系统架构层次**：
- **环境层**：多AGV仓储环境、地图管理、任务生成、碰撞检测
- **模型层**：单层统一注意力MAPPO模型、特征提取、动作掩码
- **训练层**：课程学习管理、经验回放、超参数调优
- **评估层**：指标计算、可视化、对比分析

### 2.2 环境设计规格

#### 2.2.1 物理环境配置

**地图布局规格**（严格按照integrated_research_proposal.md）：
- **地图尺寸**：26×10网格世界（260个网格单元）
- **货架配置**：15个货架，每个货架4×2网格，3行5列分布
  - 货架尺寸：每个货架占据4×2网格单元
  - 货架排列：3行5列规则分布在地图中央区域
  - 货架间距：货架间及边界处设置宽度为1的通行区域
- **卸货区域**：
  - **左上角卸货点**：位于地图左上角区域
  - **右上角卸货点**：位于地图右上角区域
  - 卸货区域为所有运输任务的最终目的地
- **通道系统**：
  - **通道宽度**：货架间及边界处宽度为1格的通行区域
  - **通道布局**：确保所有货架和卸货区域的可达性

**AGV系统配置**：
- **AGV数量**：4个同构AGV单元
- **载重能力**：每个AGV最大载重25单位
- **移动规格**：每时间步移动1个网格单元
- **初始位置**：地图下方的AGV停车区域
- **运动约束**：只能进行上下左右四方向移动

**任务系统配置**：
- **任务总数**：16个运输任务
- **任务类型**：货物从货架位置运输到卸货区域
- **任务重量**：5或10单位（随机分配，比例7:9）
- **任务分布**：任务分布在货架之上而非可通行区域中
- **任务目标**：所有任务的目标位置为卸货区域（左上角和右上角不做区分）
- **起点位置**：随机分布在15个货架位置
- **卸货机制**：AGV携带多个任务前往卸货区域后一次完成全部卸货

#### 2.2.2 状态空间设计
**AGV状态向量**（5维）：
- 位置坐标归一化：$(x_{norm}, y_{norm}) \in [0,1]^2$
- 载重信息：$load_{norm} = \frac{current\_load}{max\_capacity} \in [0,1]$
- 目标任务：$target_{norm} = \frac{target\_id}{total\_tasks} \in [0,1]$（-1表示无目标任务）
- 任务队列长度：$queue_{norm} = \frac{queue\_length}{max\_queue} \in [0,1]$

**任务状态向量**（4维）：
- 位置坐标归一化：$(x_{norm}, y_{norm}) \in [0,1]^2$（货架位置）
- 重量信息：$weight_{norm} \in \{0.5, 1.0\}$（对应5或10单位）
- 状态信息：$status \in \{0,1,2\}$（未分配/已分配/已完成）

**环境全局状态**（8维）：
- 已完成任务数归一化：$completed_{norm} = \frac{N_{completed}}{16} \in [0,1]$
- 活跃任务数归一化：$active_{norm} = \frac{N_{active}}{16} \in [0,1]$
- 平均载重利用率：$load_{util} = \frac{\sum_i LoadTime_i}{\sum_i TotalTime_i} \in [0,1]$
- 碰撞次数归一化：$collision_{norm} = \frac{N_{collisions}}{N_{max\_collisions}} \in [0,1]$
- 时间步进度：$time_{norm} = \frac{current\_step}{max\_steps} \in [0,1]$
- 系统拥堵程度：$congestion = \frac{N_{blocked\_agvs}}{N_{total\_agvs}} \in [0,1]$
- 整体系统效率：$efficiency = \frac{completed\_tasks \times avg\_path\_efficiency}{total\_time} \in [0,1]$
- 路径最优性评分：$path_{opt} = \frac{\sum_i optimal\_path_i}{\sum_i actual\_path_i} \in [0,1]$

#### 2.2.3 动作空间设计
**层次化动作空间**：
- **高层动作**（18维）：多任务选择动作
  - 保持当前任务组合（动作0）：继续执行当前的任务组合
  - 选择新任务（动作1-16）：在载重允许的情况下选择新任务加入当前组合
  - 前往卸货区域（动作17）：携带当前所有任务前往卸货区域
- **低层动作**（5维）：运动控制动作，包括四方向移动和原地等待

**多任务选择机制**：
- AGV可以同时选择多个任务，直到达到载重限制
- 每次选择新任务时，系统自动检查载重约束和路径合理性
- 支持动态任务组合优化，在执行过程中调整任务选择

**智能动作掩码机制**：
- **载重感知掩码**：基于当前载重和剩余容量，屏蔽不可行的任务选择
- **路径优化掩码**：考虑任务位置分布，优先选择路径合理的任务组合
- **协作避让掩码**：基于其他AGV的位置和意图，避免冲突选择

## 3. 多载重AGV自主调度机制

### 3.0 多载重AGV核心实现机制

#### 3.0.1 自主任务选择实现
**观测机制**：
- 每个AGV维护局部观测空间，包含自身状态和可见任务信息
- 实时更新任务可用性、重量信息和位置分布
- 动态计算剩余载重容量和可选任务组合

**决策流程**：
1. **载重评估**：计算当前载重利用率和剩余容量
2. **任务筛选**：基于载重约束筛选可行任务集合
3. **组合优化**：评估不同任务组合的载重效率和路径成本
4. **最优选择**：通过注意力机制选择最优任务组合

#### 3.0.2 载重利用率优化实现
**多任务组合策略**：
- 支持AGV同时选择多个兼容任务（重量总和≤25单位）
- 优先选择能够最大化载重利用率的任务组合
- 考虑任务位置分布，避免过度绕行

**动态载重平衡**：
- 实时监控系统整体载重分布
- 通过协作机制避免载重不均衡
- 动态调整任务分配策略，优化全局载重效率

#### 3.0.3 路径-载重权衡机制
**权衡策略**：
- 在载重利用率和路径长度间寻找最优平衡点
- 使用多目标优化方法，同时考虑载重效益和路径成本
- 根据系统状态动态调整权衡参数

**实现方法**：
- 通过奖励函数设计引导AGV学习合理的权衡策略
- 使用约束增强注意力机制，在决策时同时考虑两个目标
- 通过课程学习逐步提高权衡决策的复杂度

## 4. 单层AGV-Task-AGV注意力机制设计

### 4.1 统一注意力架构

#### 4.1.1 设计原理
**核心思想**：通过单一的异构注意力机制同时实现任务分配和AGV协作感知。将AGV和Task构建为异构图结构，通过统一的注意力计算同时处理AGV-Task交互（任务分配）和AGV-AGV交互（协作感知），避免双层架构的复杂性和训练不稳定问题。

**统一架构优势**：
- 单一网络结构，训练稳定性高
- 自然融合任务分配和协作信息
- 计算复杂度大幅降低
- 更容易调试和可视化
- 避免多层优化冲突

#### 4.1.2 异构图注意力设计
**图结构定义**：
- **节点类型**：AGV节点 + Task节点
- **边类型**：
  - AGV-Task边：表示AGV对任务的选择倾向
  - AGV-AGV边：表示AGV间的协作关系
  - Task-AGV边：表示任务对AGV的吸引力（可选）

**数学表示**：
设AGV $i$ 的嵌入表示为 $\mathbf{h}_{agv}^i \in \mathbb{R}^{d}$，任务 $j$ 的嵌入表示为 $\mathbf{h}_{task}^j \in \mathbb{R}^{d}$，则统一注意力机制为：

$$\mathbf{Q}_i = \mathbf{h}_{agv}^i \mathbf{W}_Q, \quad \mathbf{K}_j = \mathbf{h}_{node}^j \mathbf{W}_K, \quad \mathbf{V}_j = \mathbf{h}_{node}^j \mathbf{W}_V$$

其中 $\mathbf{h}_{node}^j$ 可以是任务节点或其他AGV节点。

#### 4.1.3 双重注意力计算
**任务分配注意力**（AGV关注Task）：
$$\alpha_{ij}^{task} = \frac{\exp(\mathbf{Q}_i \cdot \mathbf{K}_j^{task T} / \sqrt{d_k} + C_{ij}^{task})}{\sum_{k=1}^M \exp(\mathbf{Q}_i \cdot \mathbf{K}_k^{task T} / \sqrt{d_k} + C_{ik}^{task})}$$

**协作感知注意力**（AGV关注AGV）：
$$\alpha_{ij}^{collab} = \frac{\exp(\mathbf{Q}_i \cdot \mathbf{K}_j^{agv T} / \sqrt{d_k} + C_{ij}^{collab})}{\sum_{k=1}^N \exp(\mathbf{Q}_i \cdot \mathbf{K}_k^{agv T} / \sqrt{d_k} + C_{ik}^{collab})}$$

**自适应融合**：
$$\mathbf{g}_i = \sigma(\mathbf{W}_g [\mathbf{output}_{task}^i; \mathbf{output}_{collab}^i] + \mathbf{b}_g)$$
$$\mathbf{z}_i^{final} = \mathbf{g}_i \odot \mathbf{output}_{task}^i + (1 - \mathbf{g}_i) \odot \mathbf{output}_{collab}^i$$

### 4.2 约束增强机制

#### 4.2.1 任务分配约束
**距离约束**（AGV到任务起点距离）：
$$C_{distance}^{task}(i,j) = -\lambda_d \cdot \frac{d_{ij}^{start}}{d_{max}}$$

**多载重优化约束**：
$$C_{capacity}^{task}(i,j) = \begin{cases}
\lambda_c \cdot \frac{load_i + weight_j}{25} + \lambda_{util} \cdot U_{target}(i,j) & \text{if } load_i + weight_j \leq 25 \\
-\infty & \text{if } load_i + weight_j > 25
\end{cases}$$

**路径-载重平衡约束**：
$$C_{balance}^{task}(i,j) = \lambda_{path} \cdot \frac{1}{d_{ij}} + \lambda_{load} \cdot \frac{weight_j}{25}$$

**任务可行性约束**：
$$C_{feasible}^{task}(i,j) = \begin{cases}
0 & \text{if task } j \text{ is available} \\
-\infty & \text{if task } j \text{ is assigned or completed}
\end{cases}$$

#### 4.2.2 协作感知约束
**碰撞风险约束**：
$$C_{collision}^{collab}(i,j) = -\lambda_{col} \cdot \exp\left(-\frac{d_{ij}^2}{2\sigma_{col}^2}\right)$$

**载重均衡约束**：
$$C_{load\_balance}^{collab}(i,j) = \lambda_{bal} \cdot \left(1 - \frac{|util_i - util_j|}{1.0}\right)$$

**距离分层约束**：
$$C_{distance}^{collab}(i,j) = \begin{cases}
\lambda_{near} & \text{if } d_{ij} \leq 3 \\
\lambda_{mid} & \text{if } 3 < d_{ij} \leq 7 \\
\lambda_{far} & \text{if } d_{ij} > 7
\end{cases}$$

#### 4.2.3 统一约束项
**总约束增强项**：
$$C_{ij} = C_{ij}^{task} + C_{ij}^{collab}$$

### 4.3 稀疏化优化

#### 4.3.1 Top-K稀疏注意力
**任务分配稀疏化**：只关注得分最高的K=8个任务
$$\tilde{\alpha}_{ij}^{task} = \begin{cases}
\alpha_{ij}^{task} & \text{if } j \in \text{TopK}(\{\alpha_{ik}^{task}\}_{k=1}^M) \\
0 & \text{otherwise}
\end{cases}$$

**协作感知稀疏化**：只关注距离最近的K=3个AGV
$$\tilde{\alpha}_{ij}^{collab} = \begin{cases}
\alpha_{ij}^{collab} & \text{if } j \in \text{TopK}(\{\alpha_{ik}^{collab}\}_{k=1}^N) \\
0 & \text{otherwise}
\end{cases}$$

**计算复杂度**：从 $O(N \times (M + N))$ 降低到 $O(N \times (K_{task} + K_{agv}))$

## 5. MAPPO算法集成

### 5.1 注意力增强策略网络

#### 5.1.1 网络架构
**输入设计**：局部观察（5维）+ 统一注意力输出（128维）= 133维
**特征提取**：三层全连接网络 133→256→128→128维
**多头输出**：
- 任务选择头：输出18维概率分布
- 运动控制头：输出5维概率分布
- 注意力预测头：输出16维概率分布（用于一致性验证）

#### 5.1.2 层次化动作采样
**策略分布**：
$$\pi_{task}^i(a_{task}) = \text{softmax}(\mathbf{W}_{task} \mathbf{f}_i + \mathbf{b}_{task})$$
$$\pi_{motion}^i(a_{motion}) = \text{softmax}(\mathbf{W}_{motion} \mathbf{f}_i + \mathbf{b}_{motion})$$

**联合策略**：
$$\pi_i(a_i) = \pi_{task}^i(a_{task}) \cdot \pi_{motion}^i(a_{motion})$$

### 5.2 中心化价值网络

#### 5.2.1 全局价值估计
**输入信息**：
- 全局状态（92维）：4×AGV状态(5维) + 16×任务状态(4维) + 环境状态(8维) = 20+64+8=92维
- 全局注意力（512维）：4×统一注意力输出

**价值函数设计**：
$$V_{global}(\mathbf{s}_{global}) = V_{system}(\mathbf{s}_{global}) + \frac{1}{N} \sum_{i=1}^N V_i(\mathbf{s}_{global}, \mathbf{s}_i, \mathbf{attention}_i)$$

其中 $V_{system}$ 为系统级价值，$V_i$ 为个体价值贡献。

### 5.3 损失函数设计

#### 5.3.1 PPO策略损失
**层次化PPO损失**：
$$L_{PPO}^i = L_{task}^i + L_{motion}^i$$

其中：
$$L_{task}^i = \mathbb{E}_t \left[ \min \left( r_{task,t}^i(\theta) \hat{A}_t^i, \text{clip}(r_{task,t}^i(\theta), 1-\epsilon, 1+\epsilon) \hat{A}_t^i \right) \right]$$

$$L_{motion}^i = \mathbb{E}_t \left[ \min \left( r_{motion,t}^i(\theta) \hat{A}_t^i, \text{clip}(r_{motion,t}^i(\theta), 1-\epsilon, 1+\epsilon) \hat{A}_t^i \right) \right]$$

#### 5.3.2 注意力正则化损失
**稀疏性正则化**：
$$L_{sparsity} = \lambda_{sparse} \sum_i \left(\|\alpha_i^{task}\|_2^2 + \|\alpha_i^{collab}\|_2^2\right)$$

**融合一致性正则化**：
$$L_{consistency} = \lambda_{cons} \sum_i \|\mathbf{g}_i - \mathbf{g}_i^{target}\|_2^2$$

**时序平滑正则化**：
$$L_{temporal} = \lambda_{temp} \sum_{i,t} \left(\|\alpha_i^{task}(t) - \alpha_i^{task}(t-1)\|_2^2 + \|\alpha_i^{collab}(t) - \alpha_i^{collab}(t-1)\|_2^2\right)$$

#### 5.3.3 总损失函数
$$L_{total} = \sum_i L_{PPO}^i + \lambda_{value} L_{value} + \lambda_{attention} L_{attention} + \lambda_{entropy} L_{entropy}$$

其中：
- $L_{value} = \|\mathbf{V}_{pred} - \mathbf{V}_{target}\|_2^2$
- $L_{attention} = L_{sparsity} + L_{consistency} + L_{temporal}$
- $L_{entropy} = -\sum_i H(\pi_i)$

## 6. 6阶段渐进式课程学习

### 6.1 阶段设计原理
**课程学习理念**：从简单到复杂的渐进式学习，模仿人类学习过程，提高学习效率和最终性能。

**难度递增策略**：
- AGV数量递增：1→2→2→3→4→4
- 任务数量递增：2→4→8→10→14→16
- 环境复杂度递增：简化→简化→标准→标准→标准→完整

### 6.2 详细阶段配置

| 阶段 | AGV数量 | 任务数量 | 学习目标 | 成功标准 | 预计时间 |
|------|---------|----------|----------|----------|----------|
| 1 | 1 | 2 | 基础移动与单点卸货 | 完成率>90%, 路径效率>85% | 2-3小时 |
| 2 | 2 | 4 | 简单协作与双点卸货 | 完成率>85%, 碰撞率<5% | 4-6小时 |
| 3 | 2 | 8 | 任务分配与卸货平衡 | 完成率>80%, 卸货平衡>0.7 | 6-8小时 |
| 4 | 3 | 10 | 多智能体协作调度 | 完成率>75%, 协作效率>0.6 | 8-12小时 |
| 5 | 4 | 14 | 复杂环境适应 | 完成率>70%, 路径效率>0.8 | 12-16小时 |
| 6 | 4 | 16 | 完整系统掌握 | 完成率>65%, 综合性能达标 | 16-24小时 |

### 6.3 阶段转换机制

#### 6.3.1 转换条件设计
**多指标综合评估**：
- 性能达标：核心指标达到预设阈值
- 稳定性检查：性能标准差 < 0.05
- 连续成功：连续50个episode达标
- 收敛确认：学习曲线趋于平稳

**转换条件数学表示**：
设阶段 $s$ 的转换条件为：
$$\text{Advance}(s) = \text{Performance}(s) \land \text{Stability}(s) \land \text{Consistency}(s)$$

其中：
- $\text{Performance}(s) = \bigwedge_{m \in \text{Metrics}} (m \geq \text{threshold}_m^s)$
- $\text{Stability}(s) = \text{Std}(\text{performance}_{recent}) < \sigma_{max}$
- $\text{Consistency}(s) = \text{ConsecutiveSuccess} \geq N_{min}$

#### 6.3.2 自适应难度调节
**难度评估指标**：
$$\text{Difficulty} = w_1 \cdot \frac{N_{agv} \cdot N_{task}}{Area} + w_2 \cdot \text{TaskComplexity} + w_3 \cdot \text{CollaborationDemand}$$

**动态调整策略**：
- 性能过好：适当增加任务数量或复杂度
- 性能不佳：简化环境或延长训练时间
- 调整幅度：每次不超过20%的参数变化

### 6.4 技能迁移机制

#### 6.4.1 参数迁移策略
**迁移比例计算**：
$$\text{TransferRatio} = \alpha_{base} \cdot \exp(-\beta \cdot \text{StageGap})$$

其中 $\alpha_{base} = 0.8$，$\beta = 0.1$，确保相邻阶段有较高迁移比例。

**参数融合**：
$$\theta_{new} = \text{TransferRatio} \cdot \theta_{prev} + (1 - \text{TransferRatio}) \cdot \theta_{random}$$

#### 6.4.2 知识蒸馏
**教师-学生框架**：
- 教师模型：前一阶段的最优模型
- 学生模型：当前阶段的学习模型
- 蒸馏损失：$L_{distill} = \text{KL}(\pi_{student} \| \pi_{teacher})$

## 7. 奖励函数设计

### 7.1 核心奖励组件

#### 7.1.1 任务完成奖励
**基础完成奖励**：
$$R_{completion}^i = \sum_{j} \mathbb{I}_{complete}(j) \cdot (R_{base} + R_{efficiency}(j) + R_{batch}(j))$$

其中：
- $R_{base} = 10.0$：基础完成奖励
- $R_{efficiency}(j) = 2.0 \cdot \max(0, 1 - \frac{T_{actual}}{T_{expected}})$：效率奖励
- $R_{batch}(j) = 3.0 \cdot \frac{N_{batch} - 1}{N_{max\_batch}}$：批量卸货奖励，鼓励一次携带多个任务

#### 7.1.2 移动效率奖励
$$R_{movement}^i = -\alpha_{step} \cdot N_{steps} - \alpha_{idle} \cdot T_{idle} + \alpha_{efficient} \cdot \mathbb{I}_{optimal\_path}$$

参数设置：$\alpha_{step} = 0.1$，$\alpha_{idle} = 0.5$，$\alpha_{efficient} = 2.0$

#### 7.1.3 协作奖励
$$R_{collaboration}^i = \beta_{avoid} \cdot N_{avoid} - \beta_{collision} \cdot N_{collision} + \beta_{help} \cdot N_{help}$$

参数设置：$\beta_{avoid} = 1.0$，$\beta_{collision} = 5.0$，$\beta_{help} = 2.0$

#### 7.1.4 多载重优化奖励
**载重利用率奖励**：
$$R_{utilization}^i = \gamma_{util} \cdot \left(\frac{current\_load}{25}\right)^2$$
使用平方函数鼓励更高的载重利用率。

**载重效率奖励**：
$$R_{efficiency}^i = \gamma_{eff} \cdot \frac{\text{LoadTime}}{\text{TotalTime}} \cdot \frac{N_{tasks}}{N_{max\_tasks}}$$
同时考虑载重时间比例和任务数量。

**路径-载重平衡奖励**：
$$R_{balance}^i = \gamma_{balance} \cdot \left(1 - \frac{|L_{actual} - L_{optimal}|}{L_{optimal}}\right) \cdot \frac{current\_load}{25}$$
在载重较高时给予路径效率更高的权重。

**空载惩罚**：
$$R_{penalty}^i = -\gamma_{empty} \cdot \mathbb{I}_{empty\_travel} \cdot d_{travel}$$
根据空载行驶距离进行惩罚。

参数设置：$\gamma_{util} = 4.0$，$\gamma_{eff} = 2.0$，$\gamma_{balance} = 3.0$，$\gamma_{empty} = 1.0$

### 7.2 阶段性权重策略

**权重演化**：
- 早期阶段（1-2）：$\mathbf{w} = [0.5, 0.3, 0.1, 0.1]$（完成、移动、协作、载重）
- 中期阶段（3-4）：$\mathbf{w} = [0.3, 0.25, 0.25, 0.2]$
- 后期阶段（5-6）：$\mathbf{w} = [0.25, 0.2, 0.35, 0.2]$

**总奖励函数**：
$$R_{total}^i = \sum_{k} w_k^{stage} \cdot R_k^i$$

### 7.3 动态奖励调整

#### 7.3.1 性能自适应调整
**调整触发条件**：
- 某项指标持续优异：降低对应权重0.05
- 某项指标表现不佳：提高对应权重0.05
- 整体性能停滞：重新平衡权重分配

**调整约束**：
- 权重范围：$w_k \in [0.05, 0.6]$
- 权重和约束：$\sum_k w_k = 1$
- 调整频率：每1000个episode评估一次

#### 7.3.2 环境复杂度适应
**复杂度指标**：
$$\text{Complexity} = \frac{N_{agv}}{Area} + \frac{N_{task}}{N_{agv}} + \text{ObstacleDensity}$$

**适应性权重**：
$$w_{collaboration}^{adaptive} = w_{collaboration}^{base} \cdot (1 + 0.5 \cdot \text{Complexity})$$

## 8. 实验设计与评估方案

### 8.1 实验环境配置

#### 8.1.1 硬件环境
- **计算平台**：NVIDIA RTX 4070/4080 GPU
- **内存要求**：32GB RAM
- **存储空间**：500GB SSD
- **网络环境**：稳定互联网连接

#### 8.1.2 软件环境
- **操作系统**：Ubuntu 20.04 LTS / Windows 11
- **深度学习框架**：PyTorch 2.0+
- **强化学习框架**：Ray RLlib 2.5+
- **实验跟踪**：Weights & Biases

### 8.2 性能评估指标

#### 8.2.1 核心指标定义
**效率指标**：
- 任务完成率：$\eta = \frac{N_{completed}}{N_{total}}$
- 平均完成时间：$\bar{T} = \frac{1}{N_{completed}} \sum_{j} T_j$
- 系统吞吐量：$\text{Throughput} = \frac{N_{completed}}{T_{episode}}$

**质量指标**：
- 载重利用率：$\xi = \frac{\sum_i \text{LoadTime}_i}{\sum_i \text{TotalTime}_i}$
- 路径效率：$\epsilon = \frac{\sum_i L_{optimal}^i}{\sum_i L_{actual}^i}$

**协作指标**：
- 碰撞率：$\gamma = \frac{N_{collisions}}{T_{total} \times N_{agv}}$
- 协作评分：$C_{score} = 1 - \frac{\text{Var}(\text{workload})}{\text{Mean}(\text{workload})}$
- 平均等待时间：$W_{avg} = \frac{\sum_i T_{idle}^i}{N_{agv}}$


#### 8.2.2 统计分析方法
**显著性检验**：
- 使用配对t检验比较算法性能
- 显著性水平：$\alpha = 0.05$
- 多重比较校正：Bonferroni校正

**置信区间估计**：
- 计算95%置信区间：$\bar{x} \pm t_{0.025,n-1} \cdot \frac{s}{\sqrt{n}}$
- Bootstrap方法：重采样1000次估计分布
- 报告格式：均值 ± 标准差 [95% CI]

### 8.3 对比实验设计

#### 8.3.1 基准算法
1. **标准MAPPO**：不使用注意力机制的基础MAPPO算法
2. **传统调度算法**：基于规则的启发式调度方法
3. **随机策略**：随机动作选择的基准方法
4. **简化注意力MAPPO**：仅使用AGV-Task注意力的版本

#### 8.3.2 消融实验
**注意力机制消融**：
- 移除AGV-Task注意力（仅保留AGV-AGV协作）
- 移除AGV-AGV协作注意力（仅保留任务分配）
- 移除自适应融合机制（使用固定权重）
- 移除约束增强机制（使用基础注意力）

**课程学习消融**：
- 直接在最复杂环境训练
- 使用固定阶段转换时间
- 随机课程顺序训练

### 8.4 实验执行计划

#### 8.4.1 时间安排（10周）
**第1-2周**：环境搭建和基础实验
**第3-5周**：主要算法实验和6阶段训练
**第6-7周**：对比实验和消融实验
**第8-9周**：扩展实验和统计分析
**第10周**：结果验证和报告撰写

#### 8.4.2 实验协议
**训练协议**：
- 每个算法运行5次独立实验
- 每次实验包含完整的6阶段课程学习
- 记录详细的训练日志和性能指标
- 保存关键时间点的模型检查点

**评估协议**：
- 在标准测试环境中评估最终性能
- 每个模型运行100个测试episode
- 记录所有核心性能指标
- 进行统计显著性检验

## 9. 预期成果与创新贡献

### 9.1 技术成果
- **完整的单层AGV-Task-AGV注意力MAPPO算法**：理论设计和工程实现
- **6阶段课程学习框架**：可复用的渐进式学习系统
- **性能评估基准**：多维度的AGV调度评估体系
- **开源代码库**：完整的实现和实验配置

### 9.2 性能目标
**多载重AGV调度性能目标**：
- **任务完成率**：≥65%（相比基础MAPPO提升10-15%）
- **载重利用率**：≥75%（通过多任务智能组合优化）
- **平均载重率**：≥80%（AGV平均载重与最大载重的比值）
- **路径效率**：≥80%（在载重优化前提下的路径合理性）
- **载重均衡度**：≥0.85（多AGV间载重分配的均衡性）
- **碰撞率**：≤5%（通过协作注意力机制改善）
- **空载率**：≤15%（减少空载行驶时间）

**自主决策性能目标**：
- **任务选择准确率**：≥90%（AGV自主选择任务的合理性）
- **载重约束违反率**：≤2%（超载情况的发生频率）
- **决策响应时间**：≤100ms（单次任务选择决策时间）

### 9.3 学术创新贡献
**理论贡献**：
- 单层异构注意力机制在多智能体强化学习中的创新应用
- AGV-Task-AGV统一注意力框架与MAPPO算法的深度融合
- 约束增强异构图注意力计算的理论方法

**技术贡献**：
- 统一任务分配与协作感知的注意力机制设计
- 自适应课程学习转换策略
- 多维度约束融合的异构图注意力计算方法

**应用价值**：
- 为智能仓储系统提供实用的AGV调度解决方案
- 为多智能体强化学习研究提供新的技术路径
- 为相关工业应用提供可参考的实现方案

### 9.4 论文发表计划
**目标期刊/会议**：
- 主要目标：IEEE Transactions on Automation Science and Engineering
- 备选目标：ICRA、IROS等机器人学顶级会议
- 国内期刊：自动化学报、控制理论与应用

**时间规划**：
- 11月：完成论文初稿
- 12月：内部评审和修改
- 次年1月：投稿目标期刊
- 次年3-6月：根据审稿意见修改

---

**总结**：本研究方法论提供了基于单层AGV-Task-AGV注意力机制的MAPPO多AGV调度优化的完整技术路线。通过理论创新、技术实现、实验验证的系统性设计，确保研究的科学性和实用性。统一的注意力架构大幅降低了实现复杂度，提高了训练稳定性。分阶段的实施计划和详细的评估体系保证了项目的顺利完成和高质量的学术产出。
